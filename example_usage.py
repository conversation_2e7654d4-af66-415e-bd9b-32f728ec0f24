#!/usr/bin/env python3
"""
文本图像相似度计算使用示例

这个脚本展示了如何使用TextImageSimilarityCalculator来计算文本图像相似度。

使用方法:
1. 准备图像文件夹，包含.jpg, .png, .jpeg格式的图像
2. 准备文本文件夹，包含.txt格式的文本文件
3. 确保图像和文本文件的文件名相同（不包括扩展名）
4. 运行脚本

示例文件结构:
images/
    cat.jpg
    dog.jpg
    bird.png
texts/
    cat.txt      # 内容: "a cute cat sitting on the grass"
    dog.txt      # 内容: "a brown dog running in the park"
    bird.png     # 内容: "a colorful bird flying in the sky"
"""

import os
import sys
import logging
from pathlib import Path

# 添加src目录到Python路径
sys.path.append('src')

from text_image_similarity import TextImageSimilarityCalculator, print_results_summary

def create_sample_data():
    """创建示例数据用于测试"""
    # 创建示例文件夹
    os.makedirs('example_images', exist_ok=True)
    os.makedirs('example_texts', exist_ok=True)
    
    # 创建示例文本文件
    sample_texts = {
        'cat': 'a cute orange cat sitting on a wooden table',
        'dog': 'a golden retriever dog running in a green park',
        'bird': 'a colorful parrot flying in the blue sky',
        'flower': 'beautiful red roses blooming in a garden',
        'car': 'a red sports car driving on a highway'
    }
    
    for name, text in sample_texts.items():
        with open(f'example_texts/{name}.txt', 'w', encoding='utf-8') as f:
            f.write(text)
    
    print("示例文本文件已创建在 example_texts/ 文件夹中")
    print("请将对应的图像文件放入 example_images/ 文件夹中")
    print("图像文件名应该与文本文件名匹配（例如: cat.jpg, dog.png等）")

def run_similarity_calculation():
    """运行相似度计算"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 检查示例数据是否存在
    if not os.path.exists('example_images') or not os.path.exists('example_texts'):
        print("示例数据文件夹不存在，正在创建...")
        create_sample_data()
        return
    
    # 检查是否有图像文件
    image_files = []
    for ext in ['*.jpg', '*.png', '*.jpeg']:
        image_files.extend(Path('example_images').glob(ext))
    
    if not image_files:
        print("example_images/ 文件夹中没有找到图像文件")
        print("请添加一些图像文件（.jpg, .png, .jpeg格式）")
        return
    
    print(f"找到 {len(image_files)} 个图像文件")
    
    try:
        # 初始化相似度计算器
        print("正在初始化BLIP2模型...")
        calculator = TextImageSimilarityCalculator(
            device='cuda:0' if os.environ.get('CUDA_VISIBLE_DEVICES') else 'cpu',
            precision='fp16',
            model_type='pretrain'
        )
        
        # 计算相似度
        print("开始计算相似度...")
        results = calculator.process_image_text_pairs(
            image_dir='example_images',
            text_dir='example_texts',
            output_file='similarity_results.json'
        )
        
        # 打印结果摘要
        print_results_summary(results)
        
        # 打印详细结果
        print("\n=== 详细结果 ===")
        for name, result in results.items():
            print(f"\n文件: {name}")
            print(f"文本: {result['text_content']}")
            print(f"正常相似度: {result['normal_similarity']:.4f}")
            
            print("中间层跳跃相似度:")
            for layer, sim in result['intermediate_similarities'].items():
                print(f"  层 {layer}: {sim:.4f}")
            
            print("组合特征相似度:")
            for layer, sim in result['combined_similarities'].items():
                print(f"  层 {layer}: {sim:.4f}")
        
        print(f"\n完整结果已保存到 similarity_results.json")
        
    except Exception as e:
        print(f"计算过程中出现错误: {str(e)}")
        logging.error(f"Error: {str(e)}", exc_info=True)

def main():
    """主函数"""
    print("=== EVA-ViT 文本图像相似度计算示例 ===\n")
    
    # 检查是否在正确的目录中
    if not os.path.exists('src'):
        print("错误: 请在项目根目录中运行此脚本")
        print("当前目录应该包含 src/ 文件夹")
        return
    
    # 检查必要的权重文件
    if not os.path.exists('weight/eva_vit_g.pth'):
        print("警告: 未找到 weight/eva_vit_g.pth 文件")
        print("请确保EVA-ViT权重文件存在")
        print("您可能需要下载权重文件或修改代码中的路径")
    
    # 运行示例
    run_similarity_calculation()

if __name__ == "__main__":
    main()
