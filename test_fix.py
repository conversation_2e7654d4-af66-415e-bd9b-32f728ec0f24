#!/usr/bin/env python3
"""
测试修复后的文本图像相似度计算
"""

import sys
import os
import torch
import logging
from PIL import Image
import numpy as np

# 添加src目录到Python路径
sys.path.append('src')

def test_precision_fix():
    """测试精度修复"""
    print("=== 测试精度修复 ===")
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    try:
        from text_image_similarity import TextImageSimilarityCalculator
        
        # 测试fp32精度
        print("测试fp32精度...")
        calculator_fp32 = TextImageSimilarityCalculator(
            device='cuda:0' if torch.cuda.is_available() else 'cpu',
            precision='fp32',
            model_type='pretrain',
            layer_indices=[35, 36, 37, 38]
        )
        print("✓ fp32初始化成功")
        
        # 创建测试图像
        test_image = Image.fromarray(
            np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8), 'RGB'
        )
        
        # 保存测试图像
        os.makedirs('test_data', exist_ok=True)
        test_image.save('test_data/test_image.jpg')
        
        # 创建测试文本
        with open('test_data/test_image.txt', 'w', encoding='utf-8') as f:
            f.write('a test image for similarity calculation')
        
        print("✓ 测试数据创建成功")
        
        # 测试相似度计算
        print("测试相似度计算...")
        results = calculator_fp32.process_image_text_pairs(
            image_dir='test_data',
            text_dir='test_data',
            output_file='test_results.json'
        )
        
        if results:
            print("✓ 相似度计算成功")
            for name, result in results.items():
                print(f"文件: {name}")
                print(f"  正常相似度: {result['normal_similarity']:.4f}")
                print(f"  中间层相似度数量: {len(result['intermediate_similarities'])}")
                print(f"  组合相似度数量: {len(result['combined_similarities'])}")
        else:
            print("✗ 相似度计算失败 - 没有结果")
            
        # 清理测试文件
        import shutil
        if os.path.exists('test_data'):
            shutil.rmtree('test_data')
        if os.path.exists('test_results.json'):
            os.remove('test_results.json')
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("文本图像相似度计算修复测试")
    print("=" * 40)
    
    success = test_precision_fix()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 测试通过！精度问题已修复")
    else:
        print("❌ 测试失败，需要进一步调试")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
