#!/usr/bin/env python3
"""
调试模型结构
"""

import sys
import torch
import logging

# 添加src目录到Python路径
sys.path.append('src')

def debug_model_structure():
    """调试模型结构"""
    print("=== 调试模型结构 ===")
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    try:
        from lavis.models import load_model_and_preprocess
        
        print("加载BLIP2模型...")
        blip_model, vis_processors, txt_processors = load_model_and_preprocess(
            name='blip2_cir_image_diff_features', 
            model_type='pretrain', 
            is_eval=True, 
            device='cuda:0' if torch.cuda.is_available() else 'cpu'
        )
        
        print("✓ 模型加载成功")
        
        # 检查visual_encoder的类型和方法
        print(f"visual_encoder类型: {type(blip_model.visual_encoder)}")
        print(f"visual_encoder类名: {blip_model.visual_encoder.__class__.__name__}")
        
        # 检查是否有forward_with_intermediate方法
        has_method = hasattr(blip_model.visual_encoder, 'forward_with_intermediate')
        print(f"是否有forward_with_intermediate方法: {has_method}")
        
        if has_method:
            print("✓ forward_with_intermediate方法存在")
        else:
            print("✗ forward_with_intermediate方法不存在")
            print("可用的方法:")
            methods = [method for method in dir(blip_model.visual_encoder) if not method.startswith('_')]
            for method in methods[:10]:  # 只显示前10个方法
                print(f"  - {method}")
            print("  ...")
        
        # 检查模型的其他属性
        print(f"模型设备: {next(blip_model.parameters()).device}")
        print(f"模型数据类型: {next(blip_model.parameters()).dtype}")
        
        # 检查visual_encoder的属性
        if hasattr(blip_model.visual_encoder, 'blocks'):
            print(f"visual_encoder层数: {len(blip_model.visual_encoder.blocks)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("BLIP2模型结构调试")
    print("=" * 30)
    
    success = debug_model_structure()
    
    print("\n" + "=" * 30)
    if success:
        print("🎉 调试完成")
    else:
        print("❌ 调试失败")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
