#!/bin/bash

echo "==================================="
echo "EVA-ViT 文本图像相似度计算工具"
echo "==================================="
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python3"
    exit 1
fi

# 检查必要的文件
if [ ! -f "src/text_image_similarity.py" ]; then
    echo "错误: 未找到 src/text_image_similarity.py"
    echo "请确保在正确的项目目录中运行此脚本"
    exit 1
fi

echo "选择运行模式:"
echo "1. 运行测试 (不需要真实图像)"
echo "2. 运行示例 (需要示例图像)"
echo "3. 自定义计算 (需要指定图像和文本文件夹)"
echo "4. 退出"
echo

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo
        echo "运行测试模式..."
        python3 test_similarity.py
        ;;
    2)
        echo
        echo "运行示例模式..."
        python3 example_usage.py
        ;;
    3)
        echo
        echo "自定义计算模式"
        echo
        read -p "请输入图像文件夹路径: " image_dir
        read -p "请输入文本文件夹路径: " text_dir
        
        if [ ! -d "$image_dir" ]; then
            echo "错误: 图像文件夹不存在: $image_dir"
            exit 1
        fi
        
        if [ ! -d "$text_dir" ]; then
            echo "错误: 文本文件夹不存在: $text_dir"
            exit 1
        fi
        
        echo
        echo "开始计算相似度..."
        python3 src/text_image_similarity.py \
            --image_dir "$image_dir" \
            --text_dir "$text_dir" \
            --output_file similarity_results.json
        
        echo
        echo "计算完成！结果已保存到 similarity_results.json"
        ;;
    4)
        echo "退出"
        exit 0
        ;;
    *)
        echo "无效选择，请重新运行脚本"
        exit 1
        ;;
esac

echo
echo "感谢使用！"
