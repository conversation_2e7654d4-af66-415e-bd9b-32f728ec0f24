#!/usr/bin/env python3
"""
文本图像相似度计算测试脚本

这个脚本用于测试TextImageSimilarityCalculator的基本功能，
不需要真实的图像文件，使用模拟数据进行测试。
"""

import sys
import os
import torch
import numpy as np
from PIL import Image
import tempfile
import logging

# 添加src目录到Python路径
sys.path.append('src')

def create_test_image(size=(224, 224)):
    """创建测试图像"""
    # 创建一个随机的RGB图像
    image_array = np.random.randint(0, 255, (size[1], size[0], 3), dtype=np.uint8)
    image = Image.fromarray(image_array, 'RGB')
    return image

def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    try:
        # 导入模块
        from text_image_similarity import TextImageSimilarityCalculator
        print("✓ 成功导入TextImageSimilarityCalculator")
        
        # 检查CUDA可用性
        device = 'cuda:0' if torch.cuda.is_available() else 'cpu'
        print(f"✓ 使用设备: {device}")
        
        # 初始化计算器（使用CPU避免权重文件问题）
        print("正在初始化计算器...")
        calculator = TextImageSimilarityCalculator(
            device='cpu',  # 使用CPU进行测试
            precision='fp32',
            model_type='pretrain'
        )
        print("✓ 成功初始化计算器")
        
        # 创建测试图像
        test_image = create_test_image()
        image_tensor = calculator.image_transform(test_image).unsqueeze(0)
        image_tensor = image_tensor.to(calculator.device)
        print("✓ 成功创建测试图像")
        
        # 测试正常图像编码
        print("测试正常图像编码...")
        normal_features = calculator.encode_image_normal(image_tensor)
        print(f"✓ 正常特征形状: {normal_features.shape}")
        
        # 测试中间层图像编码
        print("测试中间层图像编码...")
        intermediate_features = calculator.encode_image_with_intermediate(
            image_tensor, 
            layer_indices=[5, 10, 15]
        )
        print(f"✓ 中间层特征数量: {len(intermediate_features)}")
        for layer_idx, features in intermediate_features.items():
            print(f"  层 {layer_idx}: {features.shape}")
        
        # 测试文本预处理
        print("测试文本预处理...")
        test_texts = ["a cat sitting on a table", "a dog running in the park"]
        processed_texts = calculator.preprocess_text(test_texts)
        print(f"✓ 处理后的文本数量: {len(processed_texts)}")
        
        # 测试相似度计算
        print("测试相似度计算...")
        
        # 正常相似度
        sim_normal = calculator.calculate_similarity_normal(normal_features, normal_features, processed_texts)
        print(f"✓ 正常相似度形状: {sim_normal.shape}")

        # 中间层相似度
        sim_intermediate = calculator.calculate_similarity_intermediate(
            intermediate_features, normal_features, processed_texts
        )
        print(f"✓ 中间层相似度数量: {len(sim_intermediate)}")

        # 组合相似度
        sim_combined = calculator.calculate_similarity_combined(
            normal_features, intermediate_features, normal_features, processed_texts
        )
        print(f"✓ 组合相似度数量: {len(sim_combined)}")
        
        print("\n=== 所有基本功能测试通过! ===")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        logging.error(f"Test failed: {str(e)}", exc_info=True)
        return False

def test_file_processing():
    """测试文件处理功能"""
    print("\n=== 测试文件处理功能 ===")
    
    try:
        from text_image_similarity import TextImageSimilarityCalculator
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            image_dir = os.path.join(temp_dir, 'images')
            text_dir = os.path.join(temp_dir, 'texts')
            os.makedirs(image_dir)
            os.makedirs(text_dir)
            
            # 创建测试文件
            test_data = {
                'cat': 'a cute cat sitting on a table',
                'dog': 'a brown dog running in the park'
            }
            
            for name, text in test_data.items():
                # 创建图像文件
                test_image = create_test_image()
                image_path = os.path.join(image_dir, f'{name}.jpg')
                test_image.save(image_path)
                
                # 创建文本文件
                text_path = os.path.join(text_dir, f'{name}.txt')
                with open(text_path, 'w', encoding='utf-8') as f:
                    f.write(text)
            
            print("✓ 成功创建测试文件")
            
            # 初始化计算器
            calculator = TextImageSimilarityCalculator(
                device='cpu',
                precision='fp32',
                model_type='pretrain'
            )
            
            # 处理文件
            print("处理图像文本对...")
            results = calculator.process_image_text_pairs(
                image_dir=image_dir,
                text_dir=text_dir,
                output_file=None  # 不保存文件
            )
            
            print(f"✓ 成功处理 {len(results)} 个文件对")
            
            # 验证结果
            for name, result in results.items():
                print(f"文件 {name}:")
                print(f"  正常相似度: {result['normal_similarity']:.4f}")
                print(f"  中间层相似度数量: {len(result['intermediate_similarities'])}")
                print(f"  组合相似度数量: {len(result['combined_similarities'])}")
            
            print("\n=== 文件处理功能测试通过! ===")
            return True
            
    except Exception as e:
        print(f"✗ 文件处理测试失败: {str(e)}")
        logging.error(f"File processing test failed: {str(e)}", exc_info=True)
        return False

def main():
    """主测试函数"""
    print("EVA-ViT 文本图像相似度计算工具测试")
    print("=" * 50)
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 检查环境
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA设备数量: {torch.cuda.device_count()}")
    print()
    
    # 运行测试
    test_results = []
    
    # 基本功能测试
    test_results.append(test_basic_functionality())
    
    # 文件处理测试
    test_results.append(test_file_processing())
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结:")
    passed = sum(test_results)
    total = len(test_results)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过!")
        return 0
    else:
        print("❌ 部分测试失败")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
