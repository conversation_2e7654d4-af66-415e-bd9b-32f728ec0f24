# EVA-ViT + BLIP2 文本图像相似度计算工具

这个工具使用EVA-ViT和BLIP2模型计算文本和图像之间的相似度，支持三种不同的相似度计算方法。

## 功能特性

### 三种相似度计算方法

1. **正常相似度**: 使用EVA-ViT + BLIP2的完整流程提取图像特征，与文本特征计算相似度
2. **中间层跳跃相似度**: 从EVA-ViT的中间层提取特征，然后跳跃到最后一层并通过BLIP2处理，与文本特征计算相似度
3. **组合特征相似度**: 将正常特征与中间层跳跃特征相加后，与文本特征计算相似度

### 支持的文件格式

- **图像**: `.jpg`, `.png`, `.jpeg`
- **文本**: `.txt`

## 安装要求

确保您已经安装了以下依赖：

```bash
pip install torch torchvision
pip install transformers
pip install Pillow
pip install numpy
pip install tqdm
```

## 文件结构

```
project/
├── src/
│   ├── text_image_similarity.py    # 主要的相似度计算类
│   ├── lavis/                      # LAVIS模型库
│   └── utility.py                  # 工具函数
├── weight/
│   └── eva_vit_g.pth              # EVA-ViT权重文件
├── example_usage.py               # 使用示例
└── TEXT_IMAGE_SIMILARITY_README.md
```

## 使用方法

### 1. 准备数据

创建两个文件夹，分别存放图像和文本文件：

```
images/
├── cat.jpg
├── dog.png
└── bird.jpeg

texts/
├── cat.txt      # 内容: "a cute cat sitting on the grass"
├── dog.txt      # 内容: "a brown dog running in the park"  
└── bird.txt     # 内容: "a colorful bird flying in the sky"
```

**重要**: 图像文件和文本文件的文件名（不包括扩展名）必须相同才能被识别为一对。

### 2. 命令行使用

```bash
python src/text_image_similarity.py \
    --image_dir /path/to/images \
    --text_dir /path/to/texts \
    --output_file results.json \
    --device cuda:0 \
    --precision fp16 \
    --model_type pretrain
```

#### 参数说明

- `--image_dir`: 图像文件夹路径（必需）
- `--text_dir`: 文本文件夹路径（必需）
- `--output_file`: 输出结果文件路径（默认: similarity_results.json）
- `--device`: 计算设备（默认: cuda:0）
- `--precision`: 计算精度，fp16或fp32（默认: fp16）
- `--model_type`: BLIP2模型类型（默认: pretrain）
- `--layer_indices`: 指定中间层索引（可选）
- `--log_file`: 日志文件路径（默认: similarity_calculation.log）

### 3. Python代码使用

```python
import sys
sys.path.append('src')

from text_image_similarity import TextImageSimilarityCalculator

# 初始化计算器
calculator = TextImageSimilarityCalculator(
    device='cuda:0',
    precision='fp16',
    model_type='pretrain'
)

# 计算相似度
results = calculator.process_image_text_pairs(
    image_dir='path/to/images',
    text_dir='path/to/texts',
    output_file='results.json'
)

# 查看结果
for name, result in results.items():
    print(f"文件: {name}")
    print(f"正常相似度: {result['normal_similarity']:.4f}")
    print(f"中间层相似度: {result['intermediate_similarities']}")
    print(f"组合相似度: {result['combined_similarities']}")
```

### 4. 运行示例

```bash
python example_usage.py
```

这将创建示例数据并运行相似度计算。

## 输出格式

结果将保存为JSON格式：

```json
{
  "cat": {
    "normal_similarity": 0.8234,
    "intermediate_similarities": {
      "9": 0.7891,
      "19": 0.8012,
      "29": 0.8156
    },
    "combined_similarities": {
      "9": 0.8345,
      "19": 0.8467,
      "29": 0.8523
    },
    "image_path": "/path/to/cat.jpg",
    "text_path": "/path/to/cat.txt",
    "text_content": "a cute cat sitting on the grass"
  }
}
```

## 技术细节

### BLIP2 + EVA-ViT模型

- 使用BLIP2架构结合EVA-ViT-G视觉编码器（1.4B参数）
- 图像尺寸: 224x224
- 支持中间层特征提取
- 使用Q-Former进行图像文本融合

### 文本编码

- 使用BLIP2的BERT tokenizer
- 支持最大32个token的文本长度
- 通过Q-Former进行文本图像融合编码

### 相似度计算

- 使用余弦相似度
- 所有特征向量都进行L2归一化
- 支持批量处理

## 注意事项

1. **内存使用**: BLIP2 + EVA-ViT-G是一个大型模型，需要足够的GPU内存
2. **权重文件**: 模型会自动下载所需的权重文件
3. **文件匹配**: 图像和文本文件名必须匹配
4. **文本编码**: 使用BLIP2的完整文本编码流程，包括Q-Former融合

## 故障排除

### 常见问题

1. **CUDA内存不足**: 尝试使用`--precision fp16`或减少批量大小
2. **模型加载失败**: 检查网络连接，模型会自动下载权重
3. **没有匹配的文件对**: 确保图像和文本文件名相同

### 日志查看

检查日志文件以获取详细的错误信息：

```bash
tail -f similarity_calculation.log
```

## 扩展功能

您可以通过以下方式扩展此工具：

1. 添加更多的中间层选择策略
2. 支持批量处理大型数据集
3. 添加可视化功能
4. 支持更多的图像格式
5. 集成其他视觉语言模型

## 许可证

请遵循原始LAVIS项目的许可证要求。
