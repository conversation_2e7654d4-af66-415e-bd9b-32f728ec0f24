@echo off
echo ===================================
echo EVA-ViT 文本图像相似度计算工具
echo ===================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查必要的文件
if not exist "src\text_image_similarity.py" (
    echo 错误: 未找到 src\text_image_similarity.py
    echo 请确保在正确的项目目录中运行此脚本
    pause
    exit /b 1
)

echo 选择运行模式:
echo 1. 运行测试 (不需要真实图像)
echo 2. 运行示例 (需要示例图像)
echo 3. 自定义计算 (需要指定图像和文本文件夹)
echo 4. 退出
echo.

set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" goto test
if "%choice%"=="2" goto example
if "%choice%"=="3" goto custom
if "%choice%"=="4" goto exit
echo 无效选择，请重新运行脚本
pause
exit /b 1

:test
echo.
echo 运行测试模式...
python test_similarity.py
pause
goto exit

:example
echo.
echo 运行示例模式...
python example_usage.py
pause
goto exit

:custom
echo.
echo 自定义计算模式
echo.
set /p image_dir="请输入图像文件夹路径: "
set /p text_dir="请输入文本文件夹路径: "

if not exist "%image_dir%" (
    echo 错误: 图像文件夹不存在: %image_dir%
    pause
    exit /b 1
)

if not exist "%text_dir%" (
    echo 错误: 文本文件夹不存在: %text_dir%
    pause
    exit /b 1
)

echo.
echo 开始计算相似度...
python src\text_image_similarity.py --image_dir "%image_dir%" --text_dir "%text_dir%" --output_file similarity_results.json

echo.
echo 计算完成！结果已保存到 similarity_results.json
pause
goto exit

:exit
echo.
echo 感谢使用！
exit /b 0
