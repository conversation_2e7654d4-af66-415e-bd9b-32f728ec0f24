import torch
import torch.nn.functional as F
import os
import glob
from PIL import Image
import argparse
import logging
import sys
from pathlib import Path
import json
import numpy as np
from tqdm import tqdm

# 导入必要的模块
from lavis.models.eva_vit import create_eva_vit_g
from lavis.models.clip_models.tokenizer import tokenize
from lavis.models.clip_models.model import CLIP
import utility

class TextImageSimilarityCalculator:
    """
    使用EVA-ViT计算文本图像相似度的类
    支持三种相似度计算方法：
    1. 正常的图像特征与文本特征相似度
    2. 中间层跳跃到最后一层的图像特征与文本特征相似度
    3. 正常特征与中间层跳跃特征相加后的相似度
    """
    
    def __init__(self, device='cuda:0', precision='fp16'):
        """
        初始化相似度计算器
        
        Args:
            device: 计算设备
            precision: 精度设置
        """
        self.device = torch.device(device)
        self.precision = precision
        
        # 初始化EVA-ViT模型
        self.eva_vit = self._init_eva_vit()
        
        # 初始化文本编码器（使用简化的文本编码器）
        self.text_encoder = self._init_text_encoder()
        
        # 图像预处理
        self.image_transform = utility.targetpad_transform(target_ratio=1.25, dim=224)
        
        logging.info("TextImageSimilarityCalculator initialized successfully")
    
    def _init_eva_vit(self):
        """初始化EVA-ViT模型"""
        logging.info("Initializing EVA-ViT model...")

        # 检查权重文件是否存在
        weight_path = 'weight/eva_vit_g.pth'
        if not os.path.exists(weight_path):
            logging.warning(f"Weight file not found: {weight_path}")
            logging.warning("The model will be initialized with random weights")

        try:
            model = create_eva_vit_g(
                img_size=224,
                drop_path_rate=0.4,
                use_checkpoint=False,
                precision=self.precision
            )
            model = model.to(self.device)
            model.eval()

            if self.precision == "fp16":
                model = model.half()

            logging.info("EVA-ViT model initialized successfully")
            return model

        except Exception as e:
            logging.error(f"Failed to initialize EVA-ViT model: {str(e)}")
            raise
    
    def _init_text_encoder(self):
        """初始化文本编码器"""
        logging.info("Initializing text encoder...")
        # 创建一个简单的文本编码器
        # 实际应用中建议使用预训练的CLIP文本编码器
        embed_dim = self.eva_vit.embed_dim
        vocab_size = 49408  # CLIP的词汇表大小

        text_encoder = torch.nn.Sequential(
            torch.nn.Embedding(vocab_size, 512),
            torch.nn.TransformerEncoder(
                torch.nn.TransformerEncoderLayer(
                    d_model=512,
                    nhead=8,
                    dim_feedforward=2048,
                    batch_first=True
                ),
                num_layers=6
            ),
            torch.nn.Linear(512, embed_dim)
        ).to(self.device)

        if self.precision == "fp16":
            text_encoder = text_encoder.half()

        text_encoder.eval()
        logging.info("Text encoder initialized")
        return text_encoder
    
    def encode_image_normal(self, image):
        """
        正常的图像编码
        
        Args:
            image: 输入图像张量
            
        Returns:
            图像特征张量
        """
        with torch.no_grad():
            if self.precision == "fp16":
                with torch.cuda.amp.autocast():
                    features = self.eva_vit(image)
            else:
                features = self.eva_vit(image)
            
            # 取CLS token的特征
            image_features = features[:, 0, :]  # [batch_size, embed_dim]
            return F.normalize(image_features, dim=-1)
    
    def encode_image_with_intermediate(self, image, layer_indices=None):
        """
        使用中间层跳跃到最后一层的图像编码
        
        Args:
            image: 输入图像张量
            layer_indices: 要获取的中间层索引列表
            
        Returns:
            dict: 包含各中间层特征的字典
        """
        if layer_indices is None:
            # 默认获取几个关键层
            total_layers = len(self.eva_vit.blocks)
            layer_indices = [total_layers//4, total_layers//2, 3*total_layers//4]
        
        with torch.no_grad():
            if self.precision == "fp16":
                with torch.cuda.amp.autocast():
                    intermediate_outputs = self.eva_vit.forward_with_intermediate(image, layer_indices)
            else:
                intermediate_outputs = self.eva_vit.forward_with_intermediate(image, layer_indices)
            
            # 对每个中间层输出提取CLS token并归一化
            normalized_features = {}
            for layer_idx, features in intermediate_outputs.items():
                cls_features = features[:, 0, :]  # [batch_size, embed_dim]
                normalized_features[layer_idx] = F.normalize(cls_features, dim=-1)
                
            return normalized_features
    
    def encode_text(self, texts):
        """
        编码文本

        Args:
            texts: 文本列表

        Returns:
            文本特征张量
        """
        if isinstance(texts, str):
            texts = [texts]

        # 使用CLIP的tokenizer进行文本编码
        text_tokens = tokenize(texts, context_length=77).to(self.device)

        with torch.no_grad():
            if self.precision == "fp16":
                with torch.cuda.amp.autocast():
                    # 通过文本编码器
                    text_features = self.text_encoder(text_tokens)
                    # 取序列的平均值作为文本特征
                    text_features = text_features.mean(dim=1)
            else:
                text_features = self.text_encoder(text_tokens)
                text_features = text_features.mean(dim=1)

        return F.normalize(text_features, dim=-1)

    def load_image(self, image_path):
        """
        加载并预处理图像

        Args:
            image_path: 图像文件路径

        Returns:
            预处理后的图像张量
        """
        image = Image.open(image_path).convert('RGB')
        image_tensor = self.image_transform(image).unsqueeze(0)  # 添加batch维度
        return image_tensor.to(self.device)

    def calculate_similarity_normal(self, image_features, text_features):
        """
        计算正常的图像文本相似度

        Args:
            image_features: 图像特征张量
            text_features: 文本特征张量

        Returns:
            相似度分数
        """
        similarity = torch.matmul(image_features, text_features.T)
        return similarity.cpu().numpy()

    def calculate_similarity_intermediate(self, intermediate_features, text_features):
        """
        计算中间层跳跃特征的相似度

        Args:
            intermediate_features: 中间层特征字典
            text_features: 文本特征张量

        Returns:
            各层相似度字典
        """
        similarities = {}
        for layer_idx, features in intermediate_features.items():
            similarity = torch.matmul(features, text_features.T)
            similarities[layer_idx] = similarity.cpu().numpy()
        return similarities

    def calculate_similarity_combined(self, normal_features, intermediate_features, text_features):
        """
        计算正常特征与中间层特征相加后的相似度

        Args:
            normal_features: 正常图像特征
            intermediate_features: 中间层特征字典
            text_features: 文本特征张量

        Returns:
            组合特征相似度字典
        """
        combined_similarities = {}

        for layer_idx, inter_features in intermediate_features.items():
            # 将正常特征与中间层特征相加
            combined_features = normal_features + inter_features
            # 重新归一化
            combined_features = F.normalize(combined_features, dim=-1)
            # 计算相似度
            similarity = torch.matmul(combined_features, text_features.T)
            combined_similarities[layer_idx] = similarity.cpu().numpy()

        return combined_similarities

    def process_image_text_pairs(self, image_dir, text_dir, output_file=None):
        """
        处理图像文本对并计算三种相似度

        Args:
            image_dir: 图像文件夹路径
            text_dir: 文本文件夹路径
            output_file: 输出结果文件路径

        Returns:
            相似度结果字典
        """
        # 获取图像和文本文件
        image_files = glob.glob(os.path.join(image_dir, "*.jpg")) + \
                     glob.glob(os.path.join(image_dir, "*.png")) + \
                     glob.glob(os.path.join(image_dir, "*.jpeg"))

        text_files = glob.glob(os.path.join(text_dir, "*.txt"))

        # 创建文件名到路径的映射
        image_dict = {}
        text_dict = {}

        for img_path in image_files:
            name = Path(img_path).stem
            image_dict[name] = img_path

        for txt_path in text_files:
            name = Path(txt_path).stem
            text_dict[name] = txt_path

        # 找到匹配的图像文本对
        common_names = set(image_dict.keys()) & set(text_dict.keys())

        if not common_names:
            logging.warning("No matching image-text pairs found!")
            return {}

        logging.info(f"Found {len(common_names)} matching image-text pairs")

        results = {}

        for name in tqdm(common_names, desc="Processing pairs"):
            try:
                # 加载图像
                image_tensor = self.load_image(image_dict[name])

                # 加载文本
                with open(text_dict[name], 'r', encoding='utf-8') as f:
                    text = f.read().strip()

                # 编码图像和文本
                normal_features = self.encode_image_normal(image_tensor)
                intermediate_features = self.encode_image_with_intermediate(image_tensor)
                text_features = self.encode_text([text])

                # 计算三种相似度
                sim_normal = self.calculate_similarity_normal(normal_features, text_features)
                sim_intermediate = self.calculate_similarity_intermediate(intermediate_features, text_features)
                sim_combined = self.calculate_similarity_combined(normal_features, intermediate_features, text_features)

                results[name] = {
                    'normal_similarity': float(sim_normal[0, 0]),
                    'intermediate_similarities': {str(k): float(v[0, 0]) for k, v in sim_intermediate.items()},
                    'combined_similarities': {str(k): float(v[0, 0]) for k, v in sim_combined.items()},
                    'image_path': image_dict[name],
                    'text_path': text_dict[name],
                    'text_content': text
                }

            except Exception as e:
                logging.error(f"Error processing {name}: {str(e)}")
                continue

        # 保存结果
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            logging.info(f"Results saved to {output_file}")

        return results

def print_results_summary(results):
    """
    打印结果摘要

    Args:
        results: 相似度结果字典
    """
    if not results:
        print("No results to display")
        return

    print(f"\n=== 相似度计算结果摘要 ===")
    print(f"处理的图像文本对数量: {len(results)}")

    # 计算平均相似度
    normal_sims = [r['normal_similarity'] for r in results.values()]
    avg_normal = np.mean(normal_sims)

    print(f"\n1. 正常相似度:")
    print(f"   平均值: {avg_normal:.4f}")
    print(f"   最大值: {max(normal_sims):.4f}")
    print(f"   最小值: {min(normal_sims):.4f}")

    # 中间层相似度统计
    if results:
        first_result = next(iter(results.values()))
        layer_indices = list(first_result['intermediate_similarities'].keys())

        print(f"\n2. 中间层跳跃相似度:")
        for layer_idx in layer_indices:
            layer_sims = [r['intermediate_similarities'][layer_idx] for r in results.values()]
            avg_layer = np.mean(layer_sims)
            print(f"   层 {layer_idx}: 平均值 {avg_layer:.4f}, 最大值 {max(layer_sims):.4f}, 最小值 {min(layer_sims):.4f}")

        print(f"\n3. 组合特征相似度:")
        for layer_idx in layer_indices:
            combined_sims = [r['combined_similarities'][layer_idx] for r in results.values()]
            avg_combined = np.mean(combined_sims)
            print(f"   层 {layer_idx}: 平均值 {avg_combined:.4f}, 最大值 {max(combined_sims):.4f}, 最小值 {min(combined_sims):.4f}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='计算文本图像相似度')
    parser.add_argument('--image_dir', type=str, required=True, help='图像文件夹路径')
    parser.add_argument('--text_dir', type=str, required=True, help='文本文件夹路径')
    parser.add_argument('--output_file', type=str, default='similarity_results.json', help='输出结果文件路径')
    parser.add_argument('--device', type=str, default='cuda:0', help='计算设备')
    parser.add_argument('--precision', type=str, default='fp16', choices=['fp16', 'fp32'], help='计算精度')
    parser.add_argument('--layer_indices', type=int, nargs='+', default=None,
                       help='指定要使用的中间层索引，例如: --layer_indices 10 20 30')
    parser.add_argument('--log_file', type=str, default='similarity_calculation.log', help='日志文件路径')

    args = parser.parse_args()

    # 设置日志
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(args.log_file, encoding='utf-8')
        ]
    )

    # 检查输入路径
    if not os.path.exists(args.image_dir):
        logging.error(f"图像文件夹不存在: {args.image_dir}")
        return

    if not os.path.exists(args.text_dir):
        logging.error(f"文本文件夹不存在: {args.text_dir}")
        return

    logging.info("开始计算文本图像相似度...")
    logging.info(f"图像文件夹: {args.image_dir}")
    logging.info(f"文本文件夹: {args.text_dir}")
    logging.info(f"输出文件: {args.output_file}")
    logging.info(f"计算设备: {args.device}")
    logging.info(f"计算精度: {args.precision}")

    try:
        # 初始化计算器
        calculator = TextImageSimilarityCalculator(
            device=args.device,
            precision=args.precision
        )

        # 如果指定了中间层索引，需要修改计算器的默认设置
        if args.layer_indices:
            logging.info(f"使用指定的中间层索引: {args.layer_indices}")
            # 这里可以添加设置中间层索引的逻辑

        # 处理图像文本对
        results = calculator.process_image_text_pairs(
            args.image_dir,
            args.text_dir,
            args.output_file
        )

        # 打印结果摘要
        print_results_summary(results)

        logging.info("相似度计算完成!")

    except Exception as e:
        logging.error(f"计算过程中出现错误: {str(e)}")
        raise

if __name__ == "__main__":
    main()
